<?php

namespace Modules\SalesMonitoring\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;
use \Hermawan\DataTables\DataTable;

use Modules\SalesMonitoring\Models\ExhibitorProfileModel;
use Modules\SalesMonitoring\Models\ProductlibModel;
use Modules\SalesMonitoring\Models\ReferenceModel;
use App\Models\ExhibitorEventParticipationModel;
use Modules\SalesMonitoring\Models\SalesModel;
use Modules\SalesMonitoring\Models\EventSchedModel;
use App\Models\EventSalesSettingsModel;
use Modules\SalesMonitoring\Config\SalesConfig;

use App\Entities\Sale;

class SalesController extends BaseController
{
    protected $exhibitorDB;
    protected ExhibitorProfileModel $exhibitorModel;
    protected ProductlibModel $productlibModel;
    protected ReferenceModel $referenceModel;
    protected SalesConfig $salesConfig;
    protected SalesModel $salesModel;
    protected EventSchedModel $eventschedModel;
    // protected ExhibitorEventParticipationModel $participationModel;
    // protected EventSalesSettingsModel $settingsModel;

    public function __construct(){
        $this->exhibitorDB        = db_connect('default');
        $this->exhibitorModel     = new ExhibitorProfileModel;
        $this->productlibModel    = new ProductlibModel;
        $this->referenceModel     = new ReferenceModel;
        $this->salesConfig        = new SalesConfig;
        $this->salesModel         = new SalesModel;
        $this->eventschedModel    = new EventSchedModel();
        // $this->participationModel = new ExhibitorEventParticipationModel;
        // $this->settingsModel      = new EventSalesSettingsModel;
    }

    public function dashboard($event_id = null) {
        // New professional dashboard view
        if (!$event_id) {
            return redirect()->to('exhibitor/');
        }

        $ff_code = session('user');
        $eventModel = new \App\Models\EventSchedModel();
        $event = $eventModel->where(['eventid' => $event_id])->first();

        if (!$event) {
            return redirect()->to('exhibitor/')->with('error', 'Event not found');
        }

        // Get sales statistics
        $salesStats = $this->getSalesStatistics($ff_code, $event->faircode);

        // Get countries for the sales drawer
        $countries = $this->referenceModel
            ->select('id,c_profile as country,country_code,c_code as continent')
            ->where(['switch'=>'R1'])->orderBy('c_profile','ASC')->findAll();

        $data = [
            'event' => $event,
            'stats' => $salesStats,
            'ff_code' => $ff_code,
            'countries' => $countries
        ];

        return view('Modules\SalesMonitoring\Views\exhibitors\sales\dashboard', $data);
    }

    private function getSalesStatistics($ff_code, $fair_code) {
        $salesModel = new \Modules\SalesMonitoring\Models\SalesModel();

        // Get export sales statistics
        $exportBooked = $salesModel->selectSum('cost')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code, 'type' => 'export', 'status' => 'booked'])
            ->get()->getRow()->cost ?? 0;

        $exportNegotiation = $salesModel->selectSum('cost')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code, 'type' => 'export', 'status' => 'under negotiation'])
            ->get()->getRow()->cost ?? 0;

        // Get domestic sales statistics
        $domesticBooked = $salesModel->selectSum('cost')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code, 'type' => 'domestic', 'status' => 'booked'])
            ->get()->getRow()->cost ?? 0;

        $domesticNegotiation = $salesModel->selectSum('cost')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code, 'type' => 'domestic', 'status' => 'under negotiation'])
            ->get()->getRow()->cost ?? 0;

        // Get retail sales
        $retailSales = $salesModel->selectSum('cost')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code, 'type' => 'retail'])
            ->get()->getRow()->cost ?? 0;

        // Get buyer count
        $buyerCount = $salesModel->select('buyer_name')
            ->where(['ff_code' => $ff_code, 'fair_code' => $fair_code])
            ->groupBy('buyer_name')
            ->countAllResults();

        // Get inquiry count (you may need to adjust this based on your inquiry table)
        $inquiryCount = 22; // Placeholder - implement based on your inquiry system

        return [
            'export_booked' => $exportBooked,
            'export_negotiation' => $exportNegotiation,
            'domestic_booked' => $domesticBooked,
            'domestic_negotiation' => $domesticNegotiation,
            'retail_sales' => $retailSales,
            'buyer_count' => $buyerCount,
            'inquiry_count' => $inquiryCount,
            'total_revenue' => $exportBooked + $domesticBooked + $retailSales,
            'conversion_rate' => $buyerCount > 0 ? round(($buyerCount / ($buyerCount + $inquiryCount)) * 100, 1) : 0
        ];
    }

    public function index($event_id=null)
    {
        if(!$event_id){
            return redirect()->to('/');
            exit();
        }

        $ff_code = session('user');

        $eventModel = new \Modules\SalesMonitoring\Models\EventSchedModel();
        $event = $eventModel->where(['eventid' => $event_id])->first();

        // $event      = $eventModel->where(['active'=>config('MyConfig')->activeFair])->first();
        $dates      = extractNoOfDays($event->start_date,$event->no_of_days);

        $countries  = $this->referenceModel
        ->select('id,c_profile as country,country_code,c_code as continent')
        ->where(['switch'=>'R1'])->orderBy('c_profile','ASC')->findAll();
        $products   = $this->productlibModel->getProductList($event->product_fair_code,$event->sector);

        // $profile    = $this->exhibitorModel->getContactAttendance($ff_code,session('fair_code'));
        $profile    = $this->exhibitorModel->getContactAttendance($ff_code,$event->faircode);
        
        $grouped_products = [];
        foreach ($products as $product) {
            $grouped_products[$product->c_profile][] = $product;
        }

        $status = array_map(fn($x)=>$x,$this->salesConfig->sale_status);

        // dd($grouped_products);

        return view('Modules\SalesMonitoring\Views\exhibitors\sales\index',[
            'profile'=> $profile,
            'dates'=> $dates,
            'countries' => $countries,
            'grouped_products' => $grouped_products,
            'status' => $status,
            'fair_code' => $event->faircode,
            'event_id' => $event_id,
            'sector' => $event->sector,
            'description' => $event->description,
            'dateAsCalendar' => $this->salesConfig->dateAsCalendar
        ]);
    }

    public function store($event_id=null)
    {
        if(!$event_id) {
            return redirect()->to('exhibitor/');
        }

        // Get sale type and validate it first
        $sale_type = $this->request->getPost('sale_type') ?? '';

        if (empty($sale_type)) {
            $errors = ['sale_type' => 'Please select a sale type'];
            if($this->request->isAJAX()){
                return $this->response->setJson(['status'=>'false','errors'=>$errors]);
            }
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Load appropriate validation rules based on sale type
        $validationConfig = new \Modules\SalesMonitoring\Config\Validation();

        switch ($sale_type) {
            case 'export':
                $rules = $validationConfig->exportSalePost;
                $errors = $validationConfig->exportSalePost_errors;
                break;
            case 'domestic':
                $rules = $validationConfig->domesticSalePost;
                $errors = $validationConfig->domesticSalePost_errors;
                break;
            case 'retail':
                $rules = $validationConfig->retailSalePost;
                $errors = $validationConfig->retailSalePost_errors;
                break;
            default:
                $validationErrors = ['sale_type' => 'Invalid sale type selected'];
                if($this->request->isAJAX()){
                    return $this->response->setJson(['status'=>'false','errors'=>$validationErrors]);
                }
                return redirect()->back()->withInput()->with('errors', $validationErrors);
        }

        // Validate the form data
        if($this->validate($rules, $errors) === false){
            if($this->request->isAJAX()){
                return $this->response->setJson(['status'=>'false','errors'=>$this->validator->getErrors()]);
            }
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Process the data if validation passes
        $postData = $this->request->getPost();
        $data = $this->_prepareSaleData($event_id,$postData);

        $salesEnt = new \Modules\SalesMonitoring\Entities\Sale();
        $salesEnt->fill($data);

        if($this->salesModel->save($salesEnt)){
            $last_id = $this->salesModel->insertID();
            if($this->request->isAJAX()){
                return $this->response->setJson(['status'=>'success','message'=>'Sale recorded successfully!','icon'=>'success','id'=>$last_id]);
            }
            return redirect()->back()->with('success', 'Sale recorded successfully!');
        }

        // If save failed
        $saveErrors = ['general' => 'Failed to save the sale record. Please try again.'];
        if($this->request->isAJAX()){
            return $this->response->setJson(['status'=>'false','errors'=>$saveErrors]);
        }
        return redirect()->back()->withInput()->with('errors', $saveErrors);
    }

    public function dataTable($event_id = null) {
        if (!$event_id) {
            return $this->response->setJSON(['error' => 'Event ID required']);
        }

        $event = $this->eventschedModel->where(['eventid' => $event_id])->first();

        if (!$event) {
            return $this->response->setJSON(['error' => 'Event not found']);
        }

        // Get filter parameters
        $typeFilter = $this->request->getGet('type_filter');
        $statusFilter = $this->request->getGet('status_filter');
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        $result = $this->salesModel->getSalesData(session()->get('user_id'), $event->faircode, $typeFilter, $statusFilter, $dateFrom, $dateTo);

        return DataTable::of($result)
            ->addNumbering('DT_RowIndex')
            ->add('sale_type_badge', function($row) {
                return $this->getSaleTypeBadge($row->type);
            })
            ->add('action', function($row) use ($event_id) {
                return '
                    <div class="btn-list flex-nowrap">
                        <button class="btn btn-sm btn-outline-primary edit-sale"
                                title="Edit"
                                data-id="'.$row->sales_id.'"
                                data-type="'.$row->type.'"
                                data-event="'.$event_id.'"
                                data-bs-toggle="tooltip">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                                <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                                <path d="M16 5l3 3"/>
                            </svg>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-sale"
                                title="Delete"
                                data-id="'.$row->sales_id.'"
                                data-bs-toggle="tooltip">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="4" y1="7" x2="20" y2="7"/>
                                <line x1="10" y1="11" x2="10" y2="17"/>
                                <line x1="14" y1="11" x2="14" y2="17"/>
                                <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                                <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                            </svg>
                        </button>
                    </div>
                ';
            })
            ->toJson(true);
    }

    private function getSaleTypeBadge($type) {
        $type = strtolower($type);
        $badges = [
            'export' => '<span class="badge bg-blue">Export</span>',
            'domestic' => '<span class="badge bg-green">Domestic</span>',
            'retail' => '<span class="badge bg-info">Retail</span>'
        ];

        return $badges[$type] ?? '<span class="badge bg-secondary">Unknown</span>';
    }


    public function getSalesData($event_id=null){

        // $exhData = $this->exhibitorModel->where(['ff_code' => session()->get('user')])->first();

        $eventModel = new \App\Models\EventSchedModel();
        $event      = $eventModel->where(['eventid'=>$event_id])->first();

        $typeFilter = $this->request->getGet('type');

        $builder = $this->salesModel->select('sales_id,minor_prod_desc,buyer_name,buyer_type,sale_date,country,fair_code,sector,type,status,cost');
        // Calculated columns
            $this->salesModel->select("(CASE WHEN type='export' AND `status` = 'booked' THEN cost ELSE 0 END) AS exportBooked", FALSE)
            ->select("(CASE WHEN type='export' AND status = 'under negotiation' THEN cost ELSE 0 END) AS exportUndernego", FALSE)
            ->select("(CASE WHEN type='domestic' AND status = 'booked' THEN cost ELSE 0 END) AS domesticBooked", FALSE)
            ->select("(CASE WHEN type='domestic' AND status = 'under negotiation' THEN cost ELSE 0 END) AS domesticUndernego", FALSE)
            ->select("(CASE WHEN type='retail' AND status = 'booked' THEN cost ELSE 0 END) AS retail", FALSE);

            if ($typeFilter) {
            $this->salesModel->where('type', $typeFilter);
            }
            $this->salesModel->where(['ff_code'=>session()->user,'fair_code'=>$event->faircode])
            ->orderBy('sale_date','asc')->where('deleted_at IS NULL');
        return DataTable::of($builder)
        ->addNumbering('#')
        
        ->edit('exportBooked', function($row){
            return empty($row->exportBooked) ? 0: $this->formatPrice($row->exportBooked) ;})
        ->edit('exportUndernego', function($row){
            return empty($row->exportUndernego) ? 0: $this->formatPrice($row->exportUndernego) ;})
        ->edit('domesticBooked', function($row){
            return empty($row->domesticBooked) ? 0: $this->formatPrice($row->domesticBooked) ;})
        ->edit('domesticUndernego', function($row){
            return empty($row->domesticUndernego) ? 0: $this->formatPrice($row->domesticUndernego) ;})
        ->edit('retail', function($row){
            return empty($row->retail) ? 0: $this->formatPrice($row->retail) ;})
        ->add('sale_type_badge', function($row){ return $this->sale_type_badge($row->type); })
        ->add('event_id', function($row) use ($event_id) {return $event_id;})
        ->add('action', function($row) use ($event_id) {
            return '<button class="btn btn-sm btn-info edit-sale" title="Edit" data-id="'.$row->sales_id.'" data-type="'.$row->type.'" data-event="'.$event_id.'">
                <i class="fa-solid fa-pen-to-square"></i><span class="sr-only">Edit</span></button>
                <button class="btn btn-sm btn-danger delete-sale" title="Delete" data-id="'.$row->sales_id.'">
                <i class="fas fa-trash icon-white"></i><span class="sr-only">Delete</span></button>';
        })
        ->toJson(true);
    }


    private function _prepareSaleData(int $event_id, array $data):array{
        $exhData = $this->exhibitorModel->where(['ff_code' => session()->get('user_id')])->first();

        $event      = $this->eventschedModel->where(['eventid'=>$event_id])->first();

        $productData = $this->productlibModel->getProductDetailsByCode(
            $data['product_category'],
            $event->product_fair_code,
            $event->sector
        );

        $data['cost'] = str_replace('$ ', '', $data['cost']);
        $data['cost'] = str_replace('₱ ', '', $data['cost']);

        return [
            'ff_code' => $exhData->ff_code,
            'buyer_name' => $data['buyer_name'] ?? '',
            'buyer_type' => $data['buyer_type'] ?? '',
            'sale_date' => convert_to_mysql_date($data['sale_date'] ?? date('Y-m-d')),
            'type' => $data['sale_type'],
            'status' => $data['status'],
            'country' => $data['country'] ?? '',
            'cost' => str_replace(',', '', $data['cost']),
            'fair_code' => $event->faircode,
            'sector' => $event->sector,
            'minor_prod_code' => $productData->prod_code,
            'minor_prod_desc' => $productData->prod_desc ?? '',
            'major_prod_code' => $productData->prod_cat ?? '',
            'major_prod_desc' => $productData->c_profile ?? '',
            'exh_co_name' => $exhData->co_name,
            'exh_region' => $exhData->region
        ];
    }

    private function formatPrice($number){
        return $number;
        // return number_format($number, 2, '.', ',');
    }

    public function editForm($event_id = null, $sales_id = null)
    {
        if (!$sales_id) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'No sales record specified']);
        }

        $salesModel = new \Modules\SalesMonitoring\Models\SalesModel();
        $sale = $salesModel->find($sales_id);
        
        if (!$sale) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Sales record not found']);
        }

        
        // Check if the record belongs to the current user
        if ($sale->ff_code !== session()->get('user')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }
        
        // Format date for display
        $sale->sale_date = date('Y-m-d', strtotime($sale->sale_date));
        
        // Format cost for display
        $sale->cost = number_format($sale->cost, 2, '.', ',');
        
        return $this->response->setJSON(['status' => 'success', 'data' => $sale]);
    }

    public function update($event_id = null, $sales_id = null)
    {
        if (!$sales_id) {
            return redirect()->to('exhibitor/');
        }
        
        $sale = $this->salesModel->find($sales_id);
        
        if (!$sale || $sale->ff_code !== session()->get('user')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }
        
        $sale_type = $this->request->getPost('sale_type') ?? '';

        if (empty($sale_type)) {
            $errors = ['sale_type' => 'Please select a sale type'];
            if($this->request->isAJAX()){
                return $this->response->setJson(['status'=>'false','errors'=>$errors]);
            }
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Load appropriate validation rules based on sale type
        $validationConfig = new \Modules\SalesMonitoring\Config\Validation();

        switch ($sale_type) {
            case 'export':
                $rules = $validationConfig->exportSalePost;
                $errors = $validationConfig->exportSalePost_errors;
                break;
            case 'domestic':
                $rules = $validationConfig->domesticSalePost;
                $errors = $validationConfig->domesticSalePost_errors;
                break;
            case 'retail':
                $rules = $validationConfig->retailSalePost;
                $errors = $validationConfig->retailSalePost_errors;
                break;
            default:
                $validationErrors = ['sale_type' => 'Invalid sale type selected'];
                if($this->request->isAJAX()){
                    return $this->response->setJson(['status'=>'false','errors'=>$validationErrors]);
                }
                return redirect()->back()->withInput()->with('errors', $validationErrors);
        }

        if ($this->validate($rules, $errors) === false) {
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'false', 'errors' => $this->validator->getErrors()]);
            }
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $postData = $this->request->getPost();
        if($this->request->getPost('event_id')){
            $event_id = $this->request->getPost('event_id');
        }else{
            $event_id = $this->request->getGet('event_id');
        }
        // $event_id = $this->request->getPost('event_id');
        $data = $this->_prepareSaleData($event_id, $postData);
        
        $salesEnt = new Sale($data);
        $salesEnt->sales_id = $sales_id;
        
        if ($this->salesModel->save($salesEnt)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJson(['status' => 'success', 'message' => 'Record successfully updated.']);
            }
            return redirect()->back()->with('success', 'Record successfully updated.');
        }
        
        return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
    }


    public function updateList($event_id=null){
        if(!$event_id){
            return redirect()->to('exhibitor/');
            exit();
        }

        $ff_code    = session('user');
        $eventModel = new \App\Models\EventSchedModel();
        $event      = $eventModel->where(['eventid'=>$event_id])->first();
        $dates      = extractNoOfDays($event->start_date,$event->no_of_days);
        $countries  = $this->referenceModel->where(['switch'=>'R1'])->orderBy('c_profile','ASC')->findAll();
        $products   = $this->productlibModel->getProductList(config('MyConfig')->productFairCode,config('MyConfig')->sectorCode);
        $profile    = $this->exhibitorModel->getContactAttendance($ff_code,session('fair_code'));
        // $sales      = $this->salesModel->findByExhId($ff_code,config('MyConfig')->fair_code);
        
        $grouped_products = [];
        foreach ($products as $product) {
            $grouped_products[$product->c_profile][] = $product;
        }

        $status = array_map(fn($x)=>$x,config('MyConfig')->sale_status);

        // foreach($sales as $sale){
        //     echo $sale->sale_date;echo '<br>';
        // }
        // exit();
        // dd($dates);
        
        return view('exhibitor/update_index',[
            'profile'=> $profile,
            'dates'=> $dates,
            'countries' => $countries,
            'grouped_products' => $grouped_products,
            'sale_type' => $status,
            'status' => $status,
            'fair_code' => $event->faircode,
        ]);
    }

    public function delete($event_id = null, $sales_id = null) {
        if (!$sales_id) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Sales ID required']);
        }

        $sale = $this->salesModel->find($sales_id);

        if (!$sale || $sale->ff_code !== session()->get('user_id')) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Unauthorized access']);
        }

        if ($this->salesModel->delete($sales_id)) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Record successfully deleted']);
        }

        return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete record']);
    }


    private function sale_type_badge($type){
        if($type=='Export'){
            return '<span class="badge badge-primary">Export</span>';
        }elseif($type=='Domestic'){
            return '<span class="badge badge-success">Domestic</span>';
        }else{
            return '<span class="badge badge-info">Retail</span>';
        }
    }



}



